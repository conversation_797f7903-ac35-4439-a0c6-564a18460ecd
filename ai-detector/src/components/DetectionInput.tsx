'use client'

import { useState } from 'react'
import { Upload, FileText, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DetectionInputProps {
  value: string
  onChange: (value: string) => void
  onDetect: (text: string) => void
  isDetecting: boolean
}

export function DetectionInput({ value, onChange, onDetect, isDetecting }: DetectionInputProps) {
  const [wordCount, setWordCount] = useState(0)

  const handleTextChange = (text: string) => {
    onChange(text)
    setWordCount(text.length)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const text = e.target?.result as string
        handleTextChange(text)
      }
      reader.readAsText(file)
    }
  }

  const canDetect = value.trim().length > 0 && !isDetecting

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">文本输入</h2>
        <div className="flex items-center space-x-2">
          <label className="cursor-pointer flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
            <Upload className="h-4 w-4" />
            <span>上传文件</span>
            <input
              type="file"
              accept=".txt,.md,text/plain,application/markdown"
              onChange={handleFileUpload}
              className="hidden"
            />
          </label>
        </div>
      </div>

      <div className="space-y-4">
        <div className="relative">
          <textarea
            value={value}
            onChange={(e) => handleTextChange(e.target.value)}
            placeholder="请输入或粘贴需要检测的文本内容..."
            className="w-full h-64 p-4 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-gray-900 placeholder-gray-500"
          />
          <div className="absolute bottom-3 right-3 text-sm text-gray-400">
            {wordCount} 字
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>支持 TXT、MD（DOCX 请先导出为 TXT）</span>
            </div>
          </div>
          
          <button
            onClick={() => onDetect(value)}
            disabled={!canDetect}
            className={cn(
              "px-6 py-2 rounded-lg font-medium transition-all",
              canDetect
                ? "bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            )}
          >
            {isDetecting ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>检测中...</span>
              </div>
            ) : (
              "开始检测"
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
