'use client'

import { useMemo, useState } from 'react'
import { Wand2, Copy, Download, Refresh<PERSON>w, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ComparisonView } from './ComparisonView'
import { humanizeText, APIError, type HumanizePair } from '@/lib/api'

type Sentence = {
  idx: number
  text: string
  risk: 'low' | 'medium' | 'high'
  explanation: string
}

type DetectionResultType = {
  doc_score: number
  sentences: Sentence[]
}

interface HumanizePanelProps {
  originalText: string
  detectionResult: DetectionResultType | null
  onToast?: (type: 'success' | 'error' | 'info', title: string, message?: string) => void
}

export function HumanizePanel({ originalText, detectionResult, onToast }: HumanizePanelProps) {
  const [strength, setStrength] = useState(2)
  const [style, setStyle] = useState('colloquial')
  const [fracture, setFracture] = useState('mid')
  const [isHumanizing, setIsHumanizing] = useState(false)
  const [humanizedText, setHumanizedText] = useState('')
  const [humanizePairs, setHumanizePairs] = useState<HumanizePair[]>([])
  const [showComparison, setShowComparison] = useState(false)

  // 依据检测结果给出建议强度
  const recommendedStrength = useMemo(() => {
    if (!detectionResult) return null
    const s = detectionResult.doc_score
    if (s >= 70) return 3
    if (s >= 40) return 2
    return 1
  }, [detectionResult])

  const handleHumanize = async () => {
    if (!originalText.trim()) return

    setIsHumanizing(true)
    try {
      const result = await humanizeText({
        text: originalText,
        strength,
        style,
        fracture
      })

      setHumanizedText(result.full_text)
      setHumanizePairs(result.pairs)
      setShowComparison(true)
      onToast?.('success', '改写完成', '文本已成功人化改写')
    } catch (error) {
      console.error('Humanization failed:', error)
      if (error instanceof APIError) {
        onToast?.('error', '改写失败', `服务器错误: ${error.message}`)
      } else {
        onToast?.('error', '改写失败', '请检查网络连接后重试')
      }
    } finally {
      setIsHumanizing(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      onToast?.('success', '复制成功', '文本已复制到剪贴板')
    } catch (error) {
      console.error('Copy failed:', error)
      onToast?.('error', '复制失败', '请手动复制文本')
    }
  }

  const handleDownload = () => {
    try {
      const element = document.createElement('a')
      const file = new Blob([humanizedText], { type: 'text/plain' })
      element.href = URL.createObjectURL(file)
      element.download = 'humanized-text.txt'
      document.body.appendChild(element)
      element.click()
      document.body.removeChild(element)
      onToast?.('success', '下载成功', '文件已保存到本地')
    } catch (error) {
      console.error('Download failed:', error)
      onToast?.('error', '下载失败', '请稍后重试')
    }
  }

  // 句子级对比：将原文与改写文本按句子切分并对齐
  const splitIntoSentences = (text: string) => {
    return text
      .replace(/\r\n/g, '\n')
      .split(/(?<=[。！？!?\n])|(?<=[\.!?])\s+/)
      .map(s => s.trim())
      .filter(Boolean)
  }

  const pairs = useMemo(() => {
    if (!humanizedText) return []
    const src = splitIntoSentences(originalText)
    const dst = splitIntoSentences(humanizedText)
    const len = Math.min(src.length, dst.length)
    const list: { idx: number; src: string; dst: string }[] = []
    for (let i = 0; i < len; i++) {
      if (src[i] !== dst[i]) {
        list.push({ idx: i, src: src[i], dst: dst[i] })
      }
    }
    return list
  }, [originalText, humanizedText])

  const canHumanize = originalText.trim().length > 0 && !isHumanizing

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">人化改写</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowComparison(!showComparison)}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            {showComparison ? '隐藏对比' : '显示对比'}
          </button>
        </div>
      </div>

      {detectionResult && (
        <div className="mb-4 p-3 rounded-lg border bg-blue-50 border-blue-200 text-sm text-blue-800">
          检测评分：<span className="font-semibold">{detectionResult.doc_score}</span>
          {typeof recommendedStrength === 'number' && (
            <span className="ml-2">建议改写强度：{recommendedStrength}</span>
          )}
        </div>
      )}

      {/* 参数设置 */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            改写强度: {strength}
          </label>
          <input
            type="range"
            min="0"
            max="3"
            value={strength}
            onChange={(e) => setStrength(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>保守</span>
            <span>适中</span>
            <span>激进</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            写作风格
          </label>
          <select
            value={style}
            onChange={(e) => setStyle(e.target.value)}
            className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="plain">通顺自然</option>
            <option value="colloquial">口语化</option>
            <option value="restrained">克制简洁</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            断裂感
          </label>
          <select
            value={fracture}
            onChange={(e) => setFracture(e.target.value)}
            className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="low">较少停顿</option>
            <option value="mid">适中停顿</option>
            <option value="high">较多停顿</option>
          </select>
        </div>
      </div>

      {/* 改写按钮 */}
      <button
        onClick={handleHumanize}
        disabled={!canHumanize}
        className={cn(
          "w-full py-3 rounded-lg font-medium transition-all mb-6",
          canHumanize
            ? "bg-purple-600 text-white hover:bg-purple-700 shadow-md hover:shadow-lg"
            : "bg-gray-100 text-gray-400 cursor-not-allowed"
        )}
      >
        {isHumanizing ? (
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>改写中...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <Wand2 className="h-4 w-4" />
            <span>一键人化改写</span>
          </div>
        )}
      </button>

      {/* 改写结果 */}
      {humanizedText && (
        <div className="space-y-4">
          {showComparison ? (
            <ComparisonView
              originalText={originalText}
              humanizedText={humanizedText}
              pairs={humanizePairs}
              onCopy={copyToClipboard}
              onDownload={handleDownload}
              onRegenerate={handleHumanize}
            />
          ) : (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-700">改写结果</h4>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleHumanize}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="重新生成"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => copyToClipboard(humanizedText)}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="复制文本"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    onClick={handleDownload}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="下载文档"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <textarea
                value={humanizedText}
                onChange={(e) => setHumanizedText(e.target.value)}
                className="w-full h-48 p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500"
              />
            </div>
          )}
        </div>
      )}
    </div>
  )
}
